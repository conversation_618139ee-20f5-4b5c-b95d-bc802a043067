import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:file_selector/file_selector.dart';
import 'package:excel/excel.dart';
import 'package:path/path.dart' as p;
import 'dart:io';
import 'package:path_provider/path_provider.dart';

import '../models/subscriber_model.dart';
import '../services/database_service.dart';

class BulkImportPage extends StatefulWidget {
  const BulkImportPage({super.key});

  @override
  State<BulkImportPage> createState() => _BulkImportPageState();
}

class _BulkImportPageState extends State<BulkImportPage> {
  bool _isLoading = false;
  String? _selectedFilePath;
  String? _importResult;
  List<String> _excelHeaders = [];
  List<List<Data?>> _excelData = [];
  Map<String, String?> _columnMappings = {};
  List<String> _sheetNames = [];
  String? _selectedSheetName;

  // Define mappable fields with their display names
  final Map<String, String> _mappableFields = {
    'fullName': 'الاسم الكامل',
    'phoneNumber': 'رقم الهاتف',
    'packageName':
        'اسم الباقة', // Special case, not directly in SubscriberModel
    'address': 'العنوان',
    'subscriptionStart': 'تاريخ البداية',
    'subscriptionEnd': 'تاريخ النهاية',
    'macAddress': 'عنوان MAC',
    'routerName': 'اسم الراوتر',
    'technicalNotes': 'ملاحظات فنية',
    'debtAmount': 'دين سابق',
    'isActive': 'نشط؟',
    'username': 'اسم المستخدم',
    'password': 'كلمة المرور',
  };

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('استيراد مشتركين من إكسل')),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'يمكنك تحميل نموذج إكسل، تعبئته بالمشتركين، ثم رفعه لإضافة جميع المشتركين دفعة واحدة.',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    icon: const Icon(Icons.download),
                    label: const Text('تحميل نموذج إكسل'),
                    onPressed: _downloadTemplate,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton.icon(
                    icon: const Icon(Icons.upload_file),
                    label: const Text('رفع ملف إكسل'),
                    onPressed: _pickExcelFile,
                  ),
                ),
              ],
            ),
            if (_selectedFilePath != null) ...[
              const SizedBox(height: 24),
              ListTile(
                leading: const Icon(
                  Icons.insert_drive_file,
                  color: Colors.green,
                ),
                title: Text(_selectedFilePath!.split('/').last),
                subtitle: const Text('تم اختيار الملف'),
                trailing: IconButton(
                  icon: const Icon(Icons.open_in_new),
                  onPressed: () async {
                    if (_selectedFilePath != null) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('تم حفظ الملف في:\n$_selectedFilePath'),
                          duration: const Duration(seconds: 3),
                          action: SnackBarAction(
                            label: 'نسخ المسار',
                            onPressed: () {
                              // يمكن إضافة نسخ المسار هنا لاحقاً
                            },
                          ),
                        ),
                      );
                    }
                  },
                ),
              ),
              const SizedBox(height: 12),
              if (_sheetNames.isNotEmpty) ...[
                const Text(
                  'اختر الجدول (Sheet) الذي يحتوي على بيانات المشتركين:',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 12),
                DropdownButtonFormField<String>(
                  value: _selectedSheetName,
                  hint: const Text('اختر جدولاً'),
                  items: _sheetNames.map((sheetName) {
                    return DropdownMenuItem<String>(
                      value: sheetName,
                      child: Text(sheetName),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedSheetName = value;
                      // Reset headers and mappings when sheet changes
                      _excelHeaders = [];
                      _excelData = [];
                      _columnMappings = {};
                      if (value != null) {
                        _processSelectedSheet(value);
                      }
                    });
                  },
                ),
                const SizedBox(height: 24),
              ],
              if (_excelHeaders.isNotEmpty && _selectedSheetName != null) ...[
                const Text(
                  'تحديد حقول المشتركين:',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 12),
                Expanded(
                  child: ListView.builder(
                    itemCount: _excelHeaders.length,
                    itemBuilder: (context, index) {
                      final header = _excelHeaders[index];
                      return Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8.0),
                        child: Row(
                          children: [
                            Expanded(
                              flex: 2,
                              child: Text(
                                header,
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            Expanded(
                              flex: 3,
                              child: DropdownButtonFormField<String>(
                                value: _columnMappings[header],
                                hint: const Text('اختر الحقل المناسب'),
                                items: [
                                  const DropdownMenuItem<String>(
                                    value: null,
                                    child: Text('تجاهل هذا الحقل'),
                                  ),
                                  ..._mappableFields.entries.map((entry) {
                                    return DropdownMenuItem<String>(
                                      value: entry.key,
                                      child: Text(entry.value),
                                    );
                                  }),
                                ],
                                onChanged: (value) {
                                  setState(() {
                                    _columnMappings[header] = value;
                                  });
                                },
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ),
                const SizedBox(height: 12),
              ],
              ElevatedButton.icon(
                icon: const Icon(Icons.check),
                label: const Text('بدء الاستيراد'),
                onPressed: _isLoading ? null : _importSubscribers,
              ),
            ],
            if (_importResult != null) ...[
              const SizedBox(height: 24),
              Card(
                color: Colors.green.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Text(
                    _importResult!,
                    style: const TextStyle(color: Colors.green),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _downloadTemplate() async {
    setState(() => _isLoading = true);
    try {
      final excel = Excel.createExcel();
      final Sheet sheet = excel['المشتركين'];
      // رؤوس الأعمدة (متوافقة مع نموذج إضافة مشترك)
      final headers = [
        TextCellValue('الاسم الكامل'),
        TextCellValue('رقم الهاتف'),
        TextCellValue('اسم الباقة'),
        TextCellValue('العنوان'),
        TextCellValue('تاريخ البداية (yyyy-MM-dd)'),
        TextCellValue('تاريخ النهاية (yyyy-MM-dd)'),
        TextCellValue('عنوان MAC'),
        TextCellValue('اسم الراوتر'),
        TextCellValue('ملاحظات فنية'),
        TextCellValue('دين سابق'),
        TextCellValue('نشط؟ (نعم/لا)'),
        TextCellValue('اسم المستخدم'),
        TextCellValue('كلمة المرور'),
      ];
      sheet.appendRow(headers);
      // صف توضيحي كمثال
      final exampleRow = [
        TextCellValue('أحمد محمد'),
        TextCellValue('0791234567'),
        TextCellValue('باقة 50 ميجا'),
        TextCellValue('بغداد - المنصور'),
        TextCellValue('2025-05-24'),
        TextCellValue('2025-06-24'),
        TextCellValue('AA:BB:CC:DD:EE:FF'),
        TextCellValue('TP-Link'),
        TextCellValue('لا يوجد'),
        TextCellValue('0'),
        TextCellValue('نعم'),
        TextCellValue('ahmed.mohamed'),
        TextCellValue('pass123'),
      ];
      sheet.appendRow(exampleRow);
      // حفظ الملف في مجلد Downloads أو Documents
      final bytes = excel.encode()!;
      Directory? targetDir;
      try {
        targetDir = await getExternalStorageDirectory();
      } catch (_) {}
      targetDir ??= await getApplicationDocumentsDirectory();
      final filePath = p.join(targetDir.path, 'نموذج_مشتركين.xlsx');
      final file = await File(filePath).writeAsBytes(bytes);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('تم حفظ النموذج في: ${file.path}')),
      );
    } catch (e) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('خطأ في توليد النموذج: $e')));
    }
    setState(() => _isLoading = false);
  }

  void _pickExcelFile() async {
    const XTypeGroup typeGroup = XTypeGroup(
      label: 'ملفات Excel',
      extensions: <String>['xlsx', 'xls'],
      mimeTypes: <String>[
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-excel',
      ],
    );

    final XFile? file = await openFile(
      acceptedTypeGroups: <XTypeGroup>[typeGroup],
      confirmButtonText: 'اختيار',
    );

    if (file != null) {
      setState(() {
        _selectedFilePath = file.path;
        _importResult = null;
        _excelHeaders = [];
        _excelData = [];
        _columnMappings = {};
      });

      try {
        final bytes = await File(_selectedFilePath!).readAsBytes();
        final excel = Excel.decodeBytes(bytes);
        _sheetNames = excel.tables.keys.toList();

        if (_sheetNames.isNotEmpty) {
          _selectedSheetName = _sheetNames.first; // Auto-select the first sheet
          _processSelectedSheet(_selectedSheetName!);
        }
        setState(() {});
      } catch (e) {
        setState(() {
          _selectedFilePath = null;
          _importResult = 'خطأ في قراءة ملف الإكسل: $e';
          _excelHeaders = [];
          _excelData = [];
          _columnMappings = {};
        });
      }
    }
  }

  void _processSelectedSheet(String sheetName) {
    setState(() {
      _excelHeaders = [];
      _excelData = [];
      _columnMappings = {};
    });

    try {
      final bytes = File(_selectedFilePath!).readAsBytesSync();
      final excel = Excel.decodeBytes(bytes);
      final sheet = excel[sheetName];

      final rows = sheet.rows
          .where(
            (row) => row.any((cell) {
              final cellValue = cell?.value?.toString().trim();
              return cellValue != null && cellValue.isNotEmpty;
            }),
          )
          .toList();

      if (rows.isNotEmpty) {
        _excelHeaders = rows[0]
            .map((cell) => cell?.value?.toString().trim() ?? '')
            .toList();
        _excelHeaders.removeWhere((header) => header.isEmpty);

        for (var header in _excelHeaders) {
          String? suggestedMapping;
          if (header.contains('اسم') && header.contains('كامل')) {
            suggestedMapping = 'fullName';
          } else if (header.contains('هاتف')) {
            suggestedMapping = 'phoneNumber';
          } else if (header.contains('باقة')) {
            suggestedMapping = 'packageName';
          } else if (header.contains('عنوان') && !header.contains('MAC')) {
            suggestedMapping = 'address';
          } else if (header.contains('بداية')) {
            suggestedMapping = 'subscriptionStart';
          } else if (header.contains('نهاية')) {
            suggestedMapping = 'subscriptionEnd';
          } else if (header.contains('MAC')) {
            suggestedMapping = 'macAddress';
          } else if (header.contains('راوتر')) {
            suggestedMapping = 'routerName';
          } else if (header.contains('ملاحظات')) {
            suggestedMapping = 'technicalNotes';
          } else if (header.contains('دين')) {
            suggestedMapping = 'debtAmount';
          } else if (header.contains('نشط')) {
            suggestedMapping = 'isActive';
          } else if (header.contains('اسم المستخدم')) {
            suggestedMapping = 'username';
          } else if (header.contains('كلمة المرور')) {
            suggestedMapping = 'password';
          }
          _columnMappings[header] = suggestedMapping;
        }

        if (rows.length > 1) {
          _excelData = rows.sublist(1);
        }
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ في معالجة الجدول المحدد: $e')),
      );
      _excelHeaders = [];
      _excelData = [];
      _columnMappings = {};
    }
  }

  void _importSubscribers() async {
    setState(() {
      _isLoading = true;
      _importResult = null;
    });

    if (_selectedFilePath == null || _selectedSheetName == null) {
      setState(() {
        _isLoading = false;
        _importResult = 'يرجى اختيار ملف إكسل وجدول أولاً.';
      });
      return;
    }

    try {
      // Ensure all required fields are mapped
      final requiredFields = [
        'fullName',
        'phoneNumber',
        'packageName',
        'address',
        'subscriptionStart',
        'subscriptionEnd',
      ];
      for (var field in requiredFields) {
        if (!_columnMappings.containsValue(field)) {
          setState(() {
            _isLoading = false;
            _importResult = 'يرجى تحديد حقل لكل من: ${_mappableFields[field]}';
          });
          return;
        }
      }

      // جلب جميع الباقات من قاعدة البيانات
      final packages = await DatabaseService().getPackages();
      int successCount = 0;
      int failCount = 0;
      List<String> errors = [];

      for (int i = 0; i < _excelData.length; i++) {
        final row = _excelData[i];
        // Create a map to easily access cell values by their mapped field name
        final Map<String, String> rowData = {};
        for (int j = 0; j < _excelHeaders.length; j++) {
          final header = _excelHeaders[j];
          final mappedField = _columnMappings[header];
          if (mappedField != null && j < row.length) {
            rowData[mappedField] = row[j]?.value.toString().trim() ?? '';
          }
        }

        try {
          final fullName = rowData['fullName'] ?? '';
          final phoneNumber = rowData['phoneNumber'] ?? '';
          final packageName = rowData['packageName'] ?? '';
          final address = rowData['address'] ?? '';
          final startDateStr = rowData['subscriptionStart'] ?? '';
          final endDateStr = rowData['subscriptionEnd'] ?? '';
          final macAddress = rowData['macAddress'] ?? '';
          final routerName = rowData['routerName'] ?? '';
          final technicalNotes = rowData['technicalNotes'] ?? '';
          final debtAmountStr = rowData['debtAmount'] ?? '0';
          final isActiveStr = rowData['isActive'] ?? 'نعم';
          final username = rowData['username'] ?? '';
          final password = rowData['password'] ?? '';

          if (fullName.isEmpty ||
              phoneNumber.isEmpty ||
              packageName.isEmpty ||
              address.isEmpty ||
              startDateStr.isEmpty ||
              endDateStr.isEmpty) {
            failCount++;
            errors.add(
              'سطر ${i + 2}: بيانات أساسية ناقصة',
            ); // +2 because _excelData skips header and 0-indexed
            continue;
          }
          final package = packages.firstWhere(
            (p) => p.name == packageName,
            orElse: () => throw Exception('الباقة غير موجودة: $packageName'),
          );
          final startDate = DateTime.tryParse(startDateStr);
          final endDate = DateTime.tryParse(endDateStr);
          if (startDate == null || endDate == null) {
            failCount++;
            errors.add('سطر ${i + 2}: تاريخ غير صحيح');
            continue;
          }
          final debtAmount = double.tryParse(debtAmountStr) ?? 0.0;
          final isActive =
              isActiveStr == 'نعم' || isActiveStr.toLowerCase() == 'yes';
          final FirebaseFirestore firestore = FirebaseFirestore.instance;
          final subscriber = SubscriberModel(
            adminId: "",
            id: firestore.collection('subscribers').doc().id,
            fullName: fullName,
            phoneNumber: phoneNumber,
            packageId: package.serverId,
            address: address,
            paymentStatus: debtAmount > 0
                ? PaymentStatus.pending
                : PaymentStatus.paid,
            subscriptionStart: startDate,
            subscriptionEnd: endDate,
            macAddress: macAddress,
            routerName: routerName,
            technicalNotes: technicalNotes,
            debtAmount: debtAmount,
            createdAt: DateTime.now(),
            isActive: isActive,
            username: username,
            password: password,
          );
          await DatabaseService().addSubscriber(subscriber);
          successCount++;
        } catch (e) {
          failCount++;
          errors.add('سطر ${i + 2}: ${e.toString()}');
        }
      }
      setState(() {
        _isLoading = false;
        _importResult =
            'تم استيراد $successCount مشترك بنجاح.\n${failCount > 0 ? 'أخطاء: $failCount\n${errors.take(5).join('\n')}${failCount > 5 ? '\n...' : ''}' : ''}';
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _importResult = 'حدث خطأ أثناء الاستيراد: $e';
      });
    }
  }
}
