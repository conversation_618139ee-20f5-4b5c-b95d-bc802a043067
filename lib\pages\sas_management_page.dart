import 'package:flutter/material.dart';
import '../services/sas_api_service.dart';
import '../models/sas_user_model.dart';
import '../services/database_service.dart';

class SasManagementPage extends StatefulWidget {
  const SasManagementPage({super.key});

  @override
  State<SasManagementPage> createState() => _SasManagementPageState();
}

class _SasManagementPageState extends State<SasManagementPage> {
  final _formKey = GlobalKey<FormState>();
  final _hostController = TextEditingController();
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isLoading = false;
  String? _error;
  List<SasUser> _users = [];
  bool _loggedIn = false;

  @override
  void dispose() {
    _hostController.dispose();
    _usernameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _loginAndFetchUsers() async {
    print('==== [LOGIN] _loginAndFetchUsers CALLED ====');
    setState(() {
      _isLoading = true;
      _error = null;
    });
    try {
      final api = SasApiService();
      final success = await api.loginWithCredentials(
        host: _hostController.text.trim(),
        username: _usernameController.text.trim(),
        password: _passwordController.text,
      );
      if (!success) {
        setState(() {
          _error = 'فشل تسجيل الدخول. تحقق من البيانات.';
          _isLoading = false;
        });
        return;
      }
      List<SasUser> fetchedUsers = [];
      int currentPage = 1;
      int perPage = 500; // Fetch 500 users per request
      int? totalUsers;

      do {
        final usersJson = await api.getUsers(page: currentPage, perPage: perPage);
        if (usersJson != null && usersJson['data'] is List) {
          final currentBatch = (usersJson['data'] as List)
              .map((u) {
                print('DEBUG: Raw user JSON: $u'); // Add this line
                return SasUser.fromJson(u as Map<String, dynamic>);
              })
              .toList();
          fetchedUsers.addAll(currentBatch);

          // Attempt to get total users from the response, if available
          if (usersJson.containsKey('total') && usersJson['total'] is int) {
            totalUsers = usersJson['total'];
          } else if (usersJson.containsKey('meta') && usersJson['meta']['total'] is int) {
            // Common pattern for pagination metadata
            totalUsers = usersJson['meta']['total'];
          }

          // If no new users are fetched in a batch, it means we've reached the end
          if (currentBatch.isEmpty && currentPage > 1) {
            break;
          }

          currentPage++;
        } else {
          // If no data or invalid data, stop fetching
          break;
        }
      } while (totalUsers == null || fetchedUsers.length < totalUsers);

      _users = fetchedUsers;
      print('==== [LOGIN] Found ${fetchedUsers.length} users from SAS ====');
      setState(() {
        _loggedIn = true;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'خطأ: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _syncSasUsersToSubscribers() async {
    print('==== [SYNC] _syncSasUsersToSubscribers CALLED ====');
    setState(() { _isLoading = true; _error = null; });
    try {
      final databaseService = DatabaseService();
      final success = await databaseService.syncFromSas();
      
      if (success) {
        setState(() { _isLoading = false; });
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تمت مزامنة المستخدمين والباقات بنجاح.')),
        );
      } else {
        setState(() { _isLoading = false; });
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('فشل المزامنة: لم يتم تسجيل الدخول إلى SAS أو حدث خطأ.'), backgroundColor: Colors.red),
        );
      }
    } catch (e) {
      setState(() { _isLoading = false; });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('فشل المزامنة: $e'), backgroundColor: Colors.red),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('SAS Radius 4 Management'),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _loggedIn
              ? _buildUsersList()
              : _buildLoginForm(),
    );
  }

  Widget _buildLoginForm() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24.0),
      child: Form(
        key: _formKey,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            TextFormField(
              controller: _hostController,
              decoration: const InputDecoration(
                labelText: 'SAS Host (مثال: demo4.sasradius.com)',
                border: OutlineInputBorder(),
              ),
              validator: (v) => v == null || v.isEmpty ? 'أدخل عنوان السيرفر' : null,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _usernameController,
              decoration: const InputDecoration(
                labelText: 'اسم المستخدم',
                border: OutlineInputBorder(),
              ),
              validator: (v) => v == null || v.isEmpty ? 'أدخل اسم المستخدم' : null,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _passwordController,
              decoration: const InputDecoration(
                labelText: 'كلمة المرور',
                border: OutlineInputBorder(),
              ),
              obscureText: true,
              validator: (v) => v == null || v.isEmpty ? 'أدخل كلمة المرور' : null,
            ),
            const SizedBox(height: 24),
            if (_error != null)
              Text(_error!, style: const TextStyle(color: Colors.red)),
            ElevatedButton(
              onPressed: () {
                if (_formKey.currentState!.validate()) {
                  _loginAndFetchUsers();
                }
              },
              child: const Text('تسجيل الدخول وجلب المستخدمين'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUsersList() {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  'قائمة المستخدمين',
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Row(
                children: [                  ElevatedButton.icon(
                    icon: const Icon(Icons.sync),
                    label: const Text('مزامنة مع المشتركين'),
                    onPressed: _isLoading ? null : _syncSasUsersToSubscribers,
                  ),
                  const SizedBox(width: 8),
                  IconButton(
                    icon: const Icon(Icons.logout),
                    onPressed: () {
                      // فقط تسجيل الخروج عند رغبة المستخدم، لا تعيد تعيين _loggedIn و _users عند مغادرة الصفحة
                      showDialog(
                        context: context,
                        builder: (context) => AlertDialog(
                          title: const Text('تأكيد تسجيل الخروج'),
                          content: const Text('هل أنت متأكد أنك تريد تسجيل الخروج من SAS؟'),
                          actions: [
                            TextButton(
                              onPressed: () => Navigator.of(context).pop(),
                              child: const Text('إلغاء'),
                            ),
                            TextButton(
                              onPressed: () {
                                setState(() {
                                  _loggedIn = false;
                                  _users = [];
                                });
                                Navigator.of(context).pop();
                              },
                              child: const Text('تسجيل الخروج'),
                            ),
                          ],
                        ),
                      );
                    },
                    tooltip: 'تسجيل الخروج',
                  ),
                ],
              ),
            ],
          ),
        ),
        Expanded(
          child: _users.isEmpty
              ? const Center(child: Text('لا يوجد مستخدمين'))
              : ListView.builder(
                  itemCount: _users.length,
                  itemBuilder: (context, index) {
                    final user = _users[index];
                    return ListTile(
                      leading: const Icon(Icons.person),
                      title: Text(user.fullName),
                      subtitle: Text(user.username),
                      trailing: user.isActive
                          ? const Icon(Icons.check_circle, color: Colors.green)
                          : const Icon(Icons.cancel, color: Colors.red),
                    );
                  },
                ),
        ),
      ],
    );
  }
}
