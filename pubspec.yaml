name: isp_manager
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.3+2

environment:
  sdk: ^3.8.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  router_os_client: ^1.0.12
  dartssh2: ^2.12.0  # Updated version

  # Firebase dependencies
  firebase_core: ^3.6.0
  firebase_auth: ^5.3.3
  cloud_firestore: ^5.5.0
  firebase_storage: ^12.3.3
  firebase_messaging: ^15.1.3
  bluetooth_print_plus: ^2.4.6
  # For bluetooth printing
  # bluetooth_print:
  #   path: ./packages/bluetooth_print-4.3.0
  
  # Chart library for statistics
  fl_chart: ^0.68.0

  # For local storage
  shared_preferences: ^2.3.2

  # For file path operations
  path_provider: ^2.1.4

  # For HTTP requests
  http: ^1.2.2

  # For generating unique IDs
  uuid: ^4.5.1

  # For SQLite database
  sqflite: ^2.3.2

  # For path operations (used with SQLite)
  path: ^1.9.1
  # For file picking operations
  file_selector: ^1.0.3
  share_plus: ^11.0.0
  url_launcher: ^6.3.1
  intl: ^0.20.2
  excel: ^4.0.6
  sqflite_common_ffi: ^2.3.0
  teledart: ^0.6.1
  fluttertoast: ^8.2.1
 # mikrotik_mndp: ^0.0.4
  blue_thermal_printer: ^1.2.3

  flutter_localizations:
    sdk: flutter
  # flutter_sms: ^2.3.3
  bidi: ^2.0.13
  image: ^3.0.2 # Downgraded for compatibility with esc_pos_utils
  google_fonts: ^6.2.1
  esc_pos_utils: any # Allow any compatible version
  package_info_plus: ^8.0.0
  supabase_flutter: ^2.5.0
  collection: ^1.18.0 # Added for firstWhereOrNull
  local_auth: ^2.2.0 # For biometric authentication
  device_info_plus: ^11.5.0 # For device information
  json_annotation: ^4.8.1 # For JSON serialization
  
  # For SAS Radius 4 API connection
  crypto: ^3.0.2
  encrypt: ^5.0.1
  
  # للطباعة كصورة
  flutter_widget_from_html_core: ^0.14.11
  screenshot: ^3.0.0
  pdf: ^3.8.4 # Downgraded for compatibility with image ^3.0.2

  cryptojs_aes_dart:
    path: ./cryptojs_aes_dart-main
  android_intent_plus: ^5.3.0
  
  # Currency and Country picker for settings
  currency_picker: ^2.0.21
  country_picker: ^2.0.25
  shimmer: ^3.0.0

  # ZeroTier VPN for remote device access
  # zerotier_sockets:
  #   path: ./packages/zerotier_sockets-1.1.0
  zerotier_sockets: ^1.1.0
dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_launcher_icons: "^0.11.0"
  json_serializable: ^6.7.1 # For JSON code generation
  build_runner: ^2.4.7 # For running code generation

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/app_icon.png"
  min_sdk_android: 21 # android min sdk min:16, default 21
  web:
    generate: true
    image_path: "assets/app_icon.png"
    background_color: "#0175C2"
    theme_color: "#0175C2"
  windows:
    generate: true
    image_path: "assets/app_icon.png"
    icon_size: 48 # min:48, max:256, default: 48
  macos:
    generate: true
    image_path: "assets/app_icon.png"

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/
    - assets/images/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
