import 'dart:convert';
import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:http/io_client.dart' as io_client;
import 'package:isp_manager/models/user_model.dart';
import 'package:isp_manager/services/database_service.dart';
import 'aes_service.dart';
import '../models/sas_user_model.dart'; // Import SasUser model
import '../models/package_model.dart'; // Import PackageModel
import 'package:collection/collection.dart'; // Import for firstWhereOrNull
import 'package:uuid/uuid.dart'; // Import for generating UUIDs

/// خدمة API للاتصال مع SAS Radius 4
import '../models/sas_server_model.dart';
import '../services/sqlite_service.dart';

/// خدمة API للاتصال مع SAS Radius 4
class SasApiService {
  final SQLiteService _sqliteService = SQLiteService();
  String? _host;
  String? _username;
  String? _password;
  String _portal = 'acp'; // 'acp' للمدير أو 'ucp' للمستخدم
  String? _baseUrl;
  String? _token;

  /// مفتاح التشفير الثابت (نفس المستخدم في PHP)
  static const String _encryptionPassphrase =
      'abcdefghijuklmno0123456789012345';

  // إضافة خاصية للتشخيص المتقدم
  bool _debugMode = true; // يمكن تشغيل/إيقاف التشخيص المتقدم

  SasApiService(); // Default constructor

  /// تمكين أو تعطيل وضع التشخيص المتقدم
  void setDebugMode(bool enabled) {
    _debugMode = enabled;
  }

  /// دالة مساعدة لطباعة معلومات التشخيص
  void _debugPrint(String message) {
    if (_debugMode) {
      print('[SAS API DEBUG] $message');
    }
  }

  /// دالة مساعدة لتحليل استجابة الخادم وتوفير معلومات تشخيصية
  Map<String, dynamic> _analyzeServerResponse(http.Response response) {
    final analysis = <String, dynamic>{
      'status_code': response.statusCode,
      'headers': response.headers,
      'body_length': response.body.length,
      'content_type': response.headers['content-type'] ?? 'unknown',
    };

    // تحليل نوع المحتوى
    final contentType = response.headers['content-type']?.toLowerCase() ?? '';
    analysis['is_json'] = contentType.contains('application/json');
    analysis['is_html'] = contentType.contains('text/html');
    analysis['is_plain_text'] = contentType.contains('text/plain');

    // تحليل بداية المحتوى
    final bodyStart = response.body.trim();
    if (bodyStart.isNotEmpty) {
      analysis['body_starts_with'] = bodyStart.substring(
        0,
        bodyStart.length > 100 ? 100 : bodyStart.length,
      );
      analysis['starts_with_html'] = bodyStart.startsWith('<');
      analysis['starts_with_json'] =
          bodyStart.startsWith('{') || bodyStart.startsWith('[');
    }

    // البحث عن رسائل خطأ شائعة
    final bodyLower = response.body.toLowerCase();
    analysis['contains_error_keywords'] = {
      'please_connect': bodyLower.contains('please connect'),
      'sas_netgate': bodyLower.contains('sas.netgate.ltd'),
      'not_found': bodyLower.contains('not found') || bodyLower.contains('404'),
      'server_error':
          bodyLower.contains('server error') || bodyLower.contains('500'),
      'access_denied':
          bodyLower.contains('access denied') ||
          bodyLower.contains('forbidden'),
      'maintenance':
          bodyLower.contains('maintenance') ||
          bodyLower.contains('under construction'),
    };

    return analysis;
  }

  /// دالة محسنة لطباعة تفاصيل الخطأ مع التشخيص
  void _printDetailedError(
    String operation,
    http.Response response, [
    Object? exception,
  ]) {
    _debugPrint('=== تفاصيل خطأ مفصلة ===');
    _debugPrint('العملية: $operation');
    _debugPrint('المضيف: $_host');
    _debugPrint('الرابط الكامل: ${response.request?.url}');

    final analysis = _analyzeServerResponse(response);
    _debugPrint('تحليل الاستجابة: ${json.encode(analysis)}');

    if (exception != null) {
      _debugPrint('تفاصيل الاستثناء: $exception');
    }

    // طباعة جزء من محتوى الاستجابة للتشخيص
    if (response.body.isNotEmpty) {
      final preview = response.body.length > 500
          ? '${response.body.substring(0, 500)}...'
          : response.body;
      _debugPrint('محتوى الاستجابة (أول 500 حرف): $preview');
    }

    // اقتراحات للحل بناءً على التحليل
    _debugPrint('=== اقتراحات الحل ===');
    if (analysis['starts_with_html'] == true) {
      _debugPrint(
        '- الخادم يعيد HTML بدلاً من JSON. قد تكون هناك مشكلة في تكوين الخادم أو مسار API.',
      );
    }
    if (analysis['contains_error_keywords']['please_connect'] == true) {
      _debugPrint(
        '- يبدو أن الخادم يطلب الاتصال أولاً. قد تحتاج لفحص إعدادات الشبكة أو VPN.',
      );
    }
    if (analysis['status_code'] == 404) {
      _debugPrint(
        '- خطأ 404: مسار API غير موجود. تأكد من صحة الرابط وأن SAS Radius مثبت ومفعل.',
      );
    }
    if (analysis['status_code'] >= 500) {
      _debugPrint('- خطأ خادم داخلي. فحص سجلات الخادم مطلوب.');
    }
    _debugPrint('========================');
  }

  /// تسجيل الدخول والحصول على token باستخدام بيانات السيرفر المحفوظة
  Future<bool> login() async {
    try {
      final connectedServerMap = await FirebaseFirestore.instance
          .collection('sas_servers')
          .where('adminId', isEqualTo: DatabaseService().adminId)
          .where("isConnected", isEqualTo: 1)
          .get();

      final connectedServer = SasServerModel.fromMap(
        connectedServerMap.docs.first.data(),
      );

      return await loginWithCredentials(
        host: connectedServer.host,
        username: connectedServer.username,
        password: connectedServer.password,
        portal: _portal, // Use the current portal setting
      );
    } catch (e) {
      print('خطأ في تسجيل الدخول من السيرفر المحفوظ: $e');
      return false;
    }
  }

  /// محاولة تسجيل الدخول مع إرجاع معلومات مفصلة عن الخطأ
  Future<Map<String, dynamic>> loginWithDetailedResult({
    required String host,
    required String username,
    required String password,
    String portal = 'acp',
  }) async {
    final result = <String, dynamic>{
      'success': false,
      'token': null,
      'error': null,
      'error_type': null,
      'server_response': null,
    };

    _host = host;
    _username = username;
    _password = password;
    _portal = portal;

    try {
      // Determine base URL based on portal type
      String p = _portal == 'ucp' ? 'user' : 'admin';

      // Try HTTPS first
      _baseUrl = 'https://$_host/$p/api/index.php/api/';
      _debugPrint('محاولة الاتصال بـ HTTPS: $_baseUrl');

      final payload = {
        'username': _username,
        'password': _password,
        "language": "en",
      };

      String route = _portal == 'ucp' ? 'auth/login' : 'login';

      final response = await _postRequestForLogin(route, payload);

      if (response != null) {
        result['server_response'] = response;

        if (response.containsKey('token')) {
          _token = response['token'];
          result['success'] = true;
          result['token'] = _token;
          _debugPrint('تم تسجيل الدخول بنجاح عبر HTTPS');
          return result;
        } else if (response.containsKey('error')) {
          final error = response['error'].toString();
          result['error'] = error;
          result['error_type'] = _categorizeLoginError(error);

          // Don't retry with HTTP for authentication errors
          if (result['error_type'] == 'authentication') {
            return result;
          }
        }
      }

      result['error'] = 'لم يتم تلقي رد صحيح من الخادم';
      result['error_type'] = 'server_error';
      return result;
    } catch (e) {
      // Try HTTP fallback only for connectivity issues
      if (e.toString().contains('403') || e.toString().contains('401')) {
        result['error'] = e.toString();
        result['error_type'] = 'authentication';
        return result;
      }

      _debugPrint('فشل HTTPS، محاولة HTTP: $e');

      try {
        String p = _portal == 'ucp' ? 'user' : 'admin';
        _baseUrl = 'http://$_host/$p/api/index.php/api/';
        _debugPrint('محاولة الاتصال بـ HTTP: $_baseUrl');

        final payload = {
          'username': _username,
          'password': _password,
          "language": "en",
        };

        String route = _portal == 'ucp' ? 'auth/login' : 'login';

        final response = await _postRequestForLogin(route, payload);

        if (response != null) {
          result['server_response'] = response;

          if (response.containsKey('token')) {
            _token = response['token'];
            result['success'] = true;
            result['token'] = _token;
            _debugPrint('تم تسجيل الدخول بنجاح عبر HTTP');
            return result;
          } else if (response.containsKey('error')) {
            final error = response['error'].toString();
            result['error'] = error;
            result['error_type'] = _categorizeLoginError(error);
            return result;
          }
        }

        result['error'] = 'لم يتم تلقي رد صحيح من الخادم عبر HTTP';
        result['error_type'] = 'server_error';
        return result;
      } catch (httpError) {
        result['error'] = 'فشل الاتصال عبر HTTP و HTTPS: $httpError';
        result['error_type'] = 'connectivity';
        return result;
      }
    }
  }

  /// تصنيف نوع خطأ تسجيل الدخول
  String _categorizeLoginError(String error) {
    final errorLower = error.toLowerCase();

    if (errorLower.contains('invalid') ||
        errorLower.contains('password') ||
        errorLower.contains('username') ||
        errorLower.contains('credentials') ||
        errorLower.contains('rsp_invalid_username_or_password') ||
        errorLower.contains('unauthorized') ||
        errorLower.contains('forbidden')) {
      return 'authentication';
    }

    if (errorLower.contains('timeout') ||
        errorLower.contains('connection') ||
        errorLower.contains('network') ||
        errorLower.contains('unreachable')) {
      return 'connectivity';
    }

    if (errorLower.contains('server') ||
        errorLower.contains('internal') ||
        errorLower.contains('500') ||
        errorLower.contains('503')) {
      return 'server_error';
    }

    if (errorLower.contains('not found') || errorLower.contains('404')) {
      return 'endpoint_not_found';
    }

    return 'unknown';
  }

  /// الحصول على رسالة خطأ مفهومة للمستخدم
  String getHumanReadableError(String errorType, String? error) {
    switch (errorType) {
      case 'authentication':
        return 'اسم المستخدم أو كلمة المرور غير صحيحة';
      case 'connectivity':
        return 'مشكلة في الاتصال بالخادم. تحقق من اتصال الإنترنت';
      case 'server_error':
        return 'خطأ في الخادم. حاول مرة أخرى لاحقاً';
      case 'endpoint_not_found':
        return 'خدمة API غير متاحة على هذا الخادم';
      default:
        return error ?? 'خطأ غير معروف';
    }
  }

  /// الحصول على معلومات مفصلة عن حالة الاتصال لعرضها في الواجهة
  Map<String, dynamic> getConnectionStatusInfo(
    Map<String, dynamic> loginResult,
  ) {
    final success = loginResult['success'] as bool? ?? false;
    final errorType = loginResult['error_type'] as String?;
    final error = loginResult['error'] as String?;

    if (success) {
      return {
        'status': 'connected',
        'message': 'تم الاتصال بنجاح',
        'icon': Icons.check_circle,
        'color': Colors.green,
      };
    }

    switch (errorType) {
      case 'authentication':
        return {
          'status': 'auth_error',
          'message': 'خطأ في بيانات الاعتماد',
          'icon': Icons.key_off,
          'color': Colors.red,
          'suggestion': 'تأكد من صحة اسم المستخدم وكلمة المرور',
        };
      case 'connectivity':
        return {
          'status': 'connection_error',
          'message': 'مشكلة في الاتصال',
          'icon': Icons.signal_wifi_connected_no_internet_4,
          'color': Colors.orange,
          'suggestion': 'تحقق من اتصال الإنترنت وعنوان الخادم',
        };
      case 'server_error':
        return {
          'status': 'server_error',
          'message': 'خطأ في الخادم',
          'icon': Icons.dns,
          'color': Colors.red,
          'suggestion': 'حاول مرة أخرى لاحقاً أو تواصل مع المسؤول',
        };
      case 'endpoint_not_found':
        return {
          'status': 'api_error',
          'message': 'API غير متاح',
          'icon': Icons.api,
          'color': Colors.purple,
          'suggestion': 'تأكد من تثبيت وتفعيل SAS Radius على الخادم',
        };
      default:
        return {
          'status': 'unknown_error',
          'message': error ?? 'خطأ غير معروف',
          'icon': Icons.error,
          'color': Colors.grey,
          'suggestion': 'حاول مرة أخرى أو تواصل مع الدعم الفني',
        };
    }
  }

  /// تشخيص شامل لمشاكل تسجيل الدخول مع تقرير مفصل
  Future<Map<String, dynamic>> diagnoseLoginIssues({
    required String host,
    required String username,
    required String password,
    String portal = 'acp',
  }) async {
    final diagnosis = <String, dynamic>{
      'host': host,
      'username': username,
      'portal': portal,
      'timestamp': DateTime.now().toIso8601String(),
      'steps': <Map<String, dynamic>>[],
      'recommendations': <String>[],
      'overall_status': 'unknown',
    };

    _debugPrint('=== بدء التشخيص الشامل لمشاكل تسجيل الدخول ===');

    try {
      // خطوة 1: اختبار الخادم
      diagnosis['steps'].add({
        'step': 1,
        'name': 'اختبار الخادم',
        'status': 'running',
      });

      final serverTest = await testServerConnection(host: host, portal: portal);
      diagnosis['server_test'] = serverTest;
      diagnosis['steps'].last['status'] = serverTest['overall_status'];
      diagnosis['steps'].last['details'] = serverTest;

      // خطوة 2: محاولة تسجيل الدخول مع التفاصيل
      diagnosis['steps'].add({
        'step': 2,
        'name': 'محاولة تسجيل الدخول',
        'status': 'running',
      });

      final loginResult = await loginWithDetailedResult(
        host: host,
        username: username,
        password: password,
        portal: portal,
      );

      diagnosis['login_result'] = loginResult;
      diagnosis['steps'].last['status'] = loginResult['success']
          ? 'success'
          : 'failed';
      diagnosis['steps'].last['details'] = loginResult;

      // خطوة 3: تحليل النتائج وإنشاء التوصيات
      diagnosis['steps'].add({
        'step': 3,
        'name': 'تحليل النتائج',
        'status': 'running',
      });

      final recommendations = _generateDetailedRecommendations(
        serverTest,
        loginResult,
      );
      diagnosis['recommendations'] = recommendations;
      diagnosis['steps'].last['status'] = 'completed';

      // تحديد الحالة الإجمالية
      if (loginResult['success'] == true) {
        diagnosis['overall_status'] = 'success';
      } else {
        final errorType = loginResult['error_type'] as String?;
        diagnosis['overall_status'] = errorType ?? 'unknown_error';
      }
    } catch (e) {
      diagnosis['error'] = e.toString();
      diagnosis['overall_status'] = 'diagnostic_failed';
      diagnosis['recommendations'].add('فشل التشخيص: $e');
    }

    _debugPrint('=== انتهاء التشخيص الشامل ===');
    _debugPrint('النتيجة الإجمالية: ${diagnosis['overall_status']}');

    return diagnosis;
  }

  /// إنشاء توصيات مفصلة بناءً على نتائج الاختبار وتسجيل الدخول
  List<String> _generateDetailedRecommendations(
    Map<String, dynamic> serverTest,
    Map<String, dynamic> loginResult,
  ) {
    final recommendations = <String>[];

    final serverStatus = serverTest['overall_status'] as String?;
    final loginSuccess = loginResult['success'] as bool? ?? false;
    final errorType = loginResult['error_type'] as String?;

    if (loginSuccess) {
      recommendations.add('✅ تم تسجيل الدخول بنجاح! الاتصال يعمل بشكل صحيح.');
      return recommendations;
    }

    // تحليل مشاكل الخادم
    if (serverStatus == 'host_unreachable') {
      recommendations.add('🔴 الخادم غير قابل للوصول:');
      recommendations.add('  • تحقق من صحة عنوان الخادم');
      recommendations.add('  • تأكد من اتصال الإنترنت');
      recommendations.add('  • فحص إعدادات الجدار الناري');
      recommendations.add('  • جرب ping للخادم من سطر الأوامر');
    } else if (serverStatus == 'api_not_available') {
      recommendations.add('🔴 API غير متاح:');
      recommendations.add('  • تأكد من تثبيت SAS Radius بشكل صحيح');
      recommendations.add('  • فحص إعدادات خادم الويب (Apache/Nginx)');
      recommendations.add('  • تأكد من تفعيل API في إعدادات SAS');
    }

    // تحليل مشاكل تسجيل الدخول
    switch (errorType) {
      case 'authentication':
        recommendations.add('🔑 مشكلة في بيانات الاعتماد:');
        recommendations.add('  • تحقق من صحة اسم المستخدم');
        recommendations.add('  • تحقق من صحة كلمة المرور');
        recommendations.add('  • تأكد من نوع البوابة (admin/user)');
        recommendations.add('  • جرب تسجيل الدخول من المتصفح');
        break;

      case 'connectivity':
        recommendations.add('🌐 مشكلة في الاتصال:');
        recommendations.add('  • فحص اتصال الإنترنت');
        recommendations.add('  • جرب VPN إذا كان الخادم محجوب');
        recommendations.add('  • تحقق من إعدادات DNS');
        break;

      case 'server_error':
        recommendations.add('🖥️ خطأ في الخادم:');
        recommendations.add('  • فحص سجلات الخادم');
        recommendations.add('  • تحقق من مساحة القرص الصلب');
        recommendations.add('  • إعادة تشغيل خدمات SAS');
        break;
    }

    // توصيات عامة
    recommendations.add('');
    recommendations.add('💡 خطوات إضافية للتشخيص:');
    recommendations.add('  • جرب الاتصال من جهاز آخر');
    recommendations.add('  • تحقق من تحديثات SAS Radius');
    recommendations.add('  • راجع وثائق SAS للحصول على المساعدة');

    return recommendations;
  }

  /// تسجيل الدخول والحصول على token باستخدام بيانات اعتماد صريحة
  Future<bool> loginWithCredentials({
    required String host,
    required String username,
    required String password,
    String portal = 'acp', // Default to 'acp' if not provided
  }) async {
    _host = host;
    _username = username;
    _password = password;
    _portal = portal;

    try {
      // Determine base URL based on portal type ('acp' for admin, 'ucp' for user)
      String p = _portal == 'ucp' ? 'user' : 'admin';

      // Try HTTPS first for better security and compatibility
      _baseUrl = 'https://$_host/$p/api/index.php/api/';
      _debugPrint('محاولة الاتصال بـ HTTPS: $_baseUrl');

      final payload = {
        'username': _username,
        'password': _password,
        "language": "en",
      };

      // Determine endpoint based on portal type
      String route = _portal == 'ucp' ? 'auth/login' : 'login';

      final response = await _postRequestForLogin(route, payload);

      if (response != null) {
        if (response.containsKey('token')) {
          _token = response['token'];
          _debugPrint('تم تسجيل الدخول بنجاح عبر HTTPS');
          return true;
        } else if (response.containsKey('error')) {
          // Check for authentication errors that shouldn't retry with HTTP
          final error = response['error'].toString().toLowerCase();
          if (error.contains('invalid') ||
              error.contains('password') ||
              error.contains('username') ||
              error.contains('credentials') ||
              error.contains('rsp_invalid_username_or_password')) {
            _debugPrint('خطأ في بيانات الاعتماد: ${response['error']}');
            print('خطأ في بيانات الاعتماد: ${response['error']}');
            return false; // Don't retry with HTTP for auth errors
          }
        }
      }

      return false;
    } catch (e) {
      // Only try HTTP fallback for connectivity issues, not auth errors
      if (e.toString().contains('403') ||
          e.toString().contains('401') ||
          e.toString().contains('invalid') ||
          e.toString().contains('password')) {
        _debugPrint('خطأ في المصادقة، لن يتم المحاولة عبر HTTP: $e');
        print('خطأ في بيانات الاعتماد: $e');
        return false;
      }

      _debugPrint('فشل HTTPS بسبب مشكلة في الاتصال، محاولة HTTP: $e');

      // If HTTPS fails due to connectivity issues, try HTTP as fallback
      try {
        String p = _portal == 'ucp' ? 'user' : 'admin';
        _baseUrl = 'http://$_host/$p/api/index.php/api/';
        _debugPrint('محاولة الاتصال بـ HTTP: $_baseUrl');

        final payload = {
          'username': _username,
          'password': _password,
          "language": "en",
        };

        String route = _portal == 'ucp' ? 'auth/login' : 'login';

        final response = await _postRequestForLogin(route, payload);
        print("--------------------");

        if (response != null) {
          if (response.containsKey('token')) {
            _token = response['token'];
            _debugPrint('تم تسجيل الدخول بنجاح عبر HTTP');
            return true;
          } else if (response.containsKey('error')) {
            _debugPrint(
              'خطأ في بيانات الاعتماد عبر HTTP: ${response['error']}',
            );
            print('خطأ في بيانات الاعتماد: ${response['error']}');
            return false;
          }
        }

        return false;
      } catch (httpError) {
        print(
          'خطأ في تسجيل الدخول ببيانات الاعتماد (HTTP و HTTPS): $httpError',
        );
        return false;
      }
    }
  }

  /// إرسال طلب POST مع تشفير البيانات
  Future<Map<String, dynamic>?> postData(
    String route,
    Map<String, dynamic> payload,
  ) async {
    if (_token == null || _baseUrl == null) {
      throw Exception('يجب تسجيل الدخول أولاً إلى خادم SAS.');
    }

    return await _postRequest(route, payload, withAuth: true);
  }

  /// إرسال طلب PUT
  Future<Map<String, dynamic>?> putData(
    String route,
    Map<String, dynamic> payload,
  ) async {
    if (_token == null || _baseUrl == null) {
      throw Exception('يجب تسجيل الدخول أولاً إلى خادم SAS.');
    }

    return await _putRequest(route, payload, withAuth: true);
  }

  /// إرسال طلب PATCH
  Future<Map<String, dynamic>?> patchData(
    String route,
    Map<String, dynamic> payload,
  ) async {
    if (_token == null || _baseUrl == null) {
      throw Exception('يجب تسجيل الدخول أولاً إلى خادم SAS.');
    }

    return await _patchRequest(route, payload, withAuth: true);
  }

  /// إرسال طلب GET
  Future<Map<String, dynamic>?> getData(String route) async {
    if (_token == null || _baseUrl == null) {
      throw Exception('يجب تسجيل الدخول أولاً إلى خادم SAS.');
    }

    try {
      _debugPrint('إرسال طلب GET إلى: $_baseUrl$route');

      final headers = <String, String>{
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $_token',
      };
      _debugPrint('رؤوس الطلب: ${json.encode(headers)}');

      final client = _createHttpClient();

      try {
        final response = await client.get(
          Uri.parse('$_baseUrl$route'),
          headers: headers,
        );

        _debugPrint('كود الاستجابة: ${response.statusCode}');
        _debugPrint('رؤوس الاستجابة: ${json.encode(response.headers)}');

        if (response.statusCode >= 200 && response.statusCode < 400) {
          try {
            return json.decode(response.body) as Map<String, dynamic>;
          } catch (jsonError) {
            _debugPrint('خطأ في تحليل JSON: $jsonError');
            _printDetailedError('GET - تحليل JSON', response, jsonError);
            return null;
          }
        } else {
          _printDetailedError('GET', response);
          throw Exception('فشل الطلب: ${response.statusCode} ${response.body}');
        }
      } finally {
        client.close();
      }
    } catch (e) {
      _debugPrint('خطأ في طلب GET: $e');
      return null;
    }
  }

  /// دالة مساعدة لإرسال POST requests خاصة بتسجيل الدخول
  /// تتعامل مع الأخطاء بشكل مختلف ولا ترمي استثناءات للأخطاء 4xx
  Future<Map<String, dynamic>?> _postRequestForLogin(
    String route,
    Map<String, dynamic> payload,
  ) async {
    if (_baseUrl == null) {
      throw Exception('Base URL is not set. Ensure a SAS server is connected.');
    }

    try {
      _debugPrint('إرسال طلب تسجيل دخول POST إلى: $_baseUrl$route');
      _debugPrint('البيانات المرسلة: ${json.encode(payload)}');

      // تشفير البيانات
      final jsonPayload = json.encode(payload);
      final encryptedPayload = AESService.encrypt(
        data: jsonPayload,
        passphrase: _encryptionPassphrase,
      );

      final headers = <String, String>{'Content-Type': 'application/json'};

      _debugPrint('رؤوس الطلب: ${json.encode(headers)}');

      final body = json.encode({'payload': encryptedPayload});

      final client = _createHttpClient();

      try {
        final response = await client.post(
          Uri.parse('$_baseUrl$route'),
          headers: headers,
          body: body,
        );

        _debugPrint('كود الاستجابة لتسجيل الدخول: ${response.statusCode}');
        _debugPrint(
          'رؤوس الاستجابة لتسجيل الدخول: ${json.encode(response.headers)}',
        );

        // Handle 301/302 redirects specifically
        if (response.statusCode == 301 || response.statusCode == 302) {
          final location = response.headers['location'];
          if (location != null) {
            _debugPrint('تم اكتشاف إعادة توجيه إلى: $location');

            // If redirect is from HTTP to HTTPS, update base URL and retry
            if (location.startsWith('https://') &&
                _baseUrl!.startsWith('http://')) {
              _debugPrint('تحويل من HTTP إلى HTTPS، إعادة المحاولة...');
              _baseUrl = _baseUrl!.replaceFirst('http://', 'https://');

              // Retry the request with HTTPS
              final retryResponse = await client.post(
                Uri.parse('$_baseUrl$route'),
                headers: headers,
                body: body,
              );

              _debugPrint(
                'كود الاستجابة بعد إعادة التوجيه: ${retryResponse.statusCode}',
              );

              try {
                return json.decode(retryResponse.body) as Map<String, dynamic>;
              } catch (jsonError) {
                _debugPrint('خطأ في تحليل JSON بعد إعادة التوجيه: $jsonError');
                _printDetailedError(
                  'POST Login - تحليل JSON بعد إعادة التوجيه',
                  retryResponse,
                  jsonError,
                );
                return null;
              }
            }
          }
          _printDetailedError('POST Login - إعادة توجيه غير مدعومة', response);
          return null;
        }

        // For login requests, handle all status codes and try to parse JSON
        try {
          final responseData =
              json.decode(response.body) as Map<String, dynamic>;

          // For 2xx status codes, return the response normally
          if (response.statusCode >= 200 && response.statusCode < 300) {
            return responseData;
          }

          // For 4xx status codes (auth errors), return the error response without throwing
          if (response.statusCode >= 400 && response.statusCode < 500) {
            _debugPrint(
              'خطأ في المصادقة (${response.statusCode}): ${response.body}',
            );
            return responseData; // Return the error response so caller can handle it
          }

          // For 5xx status codes, treat as server error
          if (response.statusCode >= 500) {
            _printDetailedError('POST Login - خطأ خادم', response);
            throw Exception(
              'خطأ في الخادم: ${response.statusCode} ${response.body}',
            );
          }

          return responseData;
        } catch (jsonError) {
          _debugPrint('خطأ في تحليل JSON لتسجيل الدخول: $jsonError');
          _printDetailedError('POST Login - تحليل JSON', response, jsonError);

          // If it's an auth error but we can't parse JSON, still don't throw for 4xx
          if (response.statusCode >= 400 && response.statusCode < 500) {
            return {'error': 'خطأ في تحليل الاستجابة: ${response.body}'};
          }

          return null;
        }
      } finally {
        client.close();
      }
    } catch (e) {
      _debugPrint('خطأ في طلب تسجيل الدخول POST: $e');
      rethrow; // Re-throw for connectivity issues
    }
  }

  /// دالة مساعدة لإرسال POST requests
  Future<Map<String, dynamic>?> _postRequest(
    String route,
    Map<String, dynamic> payload, {
    bool withAuth = true,
  }) async {
    if (_baseUrl == null) {
      throw Exception('Base URL is not set. Ensure a SAS server is connected.');
    }

    try {
      _debugPrint('إرسال طلب POST إلى: $_baseUrl$route');
      _debugPrint('البيانات المرسلة: ${json.encode(payload)}');

      // تشفير البيانات
      final jsonPayload = json.encode(payload);
      final encryptedPayload = AESService.encrypt(
        data: jsonPayload,
        passphrase: _encryptionPassphrase,
      );

      final headers = <String, String>{'Content-Type': 'application/json'};

      if (withAuth && _token != null) {
        headers['Authorization'] = 'Bearer $_token';
      }

      _debugPrint('رؤوس الطلب: ${json.encode(headers)}');
      final body = json.encode({'payload': encryptedPayload});

      final client = _createHttpClient();

      try {
        final response = await client.post(
          Uri.parse('$_baseUrl$route'),
          headers: headers,
          body: body,
        );

        _debugPrint('كود الاستجابة: ${response.statusCode}');
        _debugPrint('رؤوس الاستجابة: ${json.encode(response.headers)}');

        // Handle 301/302 redirects specifically
        if (response.statusCode == 301 || response.statusCode == 302) {
          final location = response.headers['location'];
          if (location != null) {
            _debugPrint('تم اكتشاف إعادة توجيه إلى: $location');

            // If redirect is from HTTP to HTTPS, update base URL and retry
            if (location.startsWith('https://') &&
                _baseUrl!.startsWith('http://')) {
              _debugPrint('تحويل من HTTP إلى HTTPS، إعادة المحاولة...');
              _baseUrl = _baseUrl!.replaceFirst('http://', 'https://');
              // Retry the request with HTTPS
              final retryResponse = await client.post(
                Uri.parse('$_baseUrl$route'),
                headers: headers,
                body: body,
              );

              _debugPrint(
                'كود الاستجابة بعد إعادة التوجيه: ${retryResponse.statusCode}',
              );

              if (retryResponse.statusCode >= 200 &&
                  retryResponse.statusCode < 400) {
                try {
                  return json.decode(retryResponse.body)
                      as Map<String, dynamic>;
                } catch (jsonError) {
                  _debugPrint(
                    'خطأ في تحليل JSON بعد إعادة التوجيه: $jsonError',
                  );
                  _printDetailedError(
                    'POST - تحليل JSON بعد إعادة التوجيه',
                    retryResponse,
                    jsonError,
                  );
                  return null;
                }
              } else {
                _printDetailedError('POST بعد إعادة التوجيه', retryResponse);
                throw Exception(
                  'فشل الطلب بعد إعادة التوجيه: ${retryResponse.statusCode} ${retryResponse.body}',
                );
              }
            }
          }
          _printDetailedError('POST - إعادة توجيه غير مدعومة', response);
          throw Exception('إعادة توجيه غير مدعومة: ${response.statusCode}');
        }

        if (response.statusCode >= 200 && response.statusCode < 400) {
          try {
            return json.decode(response.body) as Map<String, dynamic>;
          } catch (jsonError) {
            _debugPrint('خطأ في تحليل JSON: $jsonError');
            _printDetailedError('POST - تحليل JSON', response, jsonError);
            return null;
          }
        } else {
          _printDetailedError('POST', response);
          throw Exception('فشل الطلب: ${response.statusCode} ${response.body}');
        }
      } finally {
        client.close();
      }
    } catch (e) {
      _debugPrint('خطأ في طلب POST: $e');
      return null;
    }
  }

  /// دالة مساعدة لإرسال PUT requests
  Future<Map<String, dynamic>?> _putRequest(
    String route,
    Map<String, dynamic> payload, {
    bool withAuth = true,
  }) async {
    if (_baseUrl == null) {
      throw Exception('Base URL is not set. Ensure a SAS server is connected.');
    }

    try {
      // تشفير البيانات
      final jsonPayload = json.encode(payload);
      final encryptedPayload = AESService.encrypt(
        data: jsonPayload,
        passphrase: _encryptionPassphrase,
      );

      final headers = <String, String>{'Content-Type': 'application/json'};

      if (withAuth && _token != null) {
        headers['Authorization'] = 'Bearer $_token';
      }

      final body = json.encode({'payload': encryptedPayload});
      final response = await http.put(
        Uri.parse('$_baseUrl$route'),
        headers: headers,
        body: body,
      );

      _debugPrint('كود الاستجابة PUT: ${response.statusCode}');
      _debugPrint('رؤوس الاستجابة PUT: ${json.encode(response.headers)}');

      if (response.statusCode >= 200 && response.statusCode < 400) {
        try {
          return json.decode(response.body) as Map<String, dynamic>;
        } catch (jsonError) {
          _debugPrint('خطأ في تحليل JSON في PUT: $jsonError');
          _printDetailedError('PUT - تحليل JSON', response, jsonError);
          return null;
        }
      } else {
        _printDetailedError('PUT', response);
        throw Exception('فشل الطلب: ${response.statusCode} ${response.body}');
      }
    } catch (e) {
      _debugPrint('خطأ في طلب PUT: $e');
      return null;
    }
  }

  /// دالة مساعدة لإرسال PATCH requests
  Future<Map<String, dynamic>?> _patchRequest(
    String route,
    Map<String, dynamic> payload, {
    bool withAuth = true,
  }) async {
    if (_baseUrl == null) {
      throw Exception('Base URL is not set. Ensure a SAS server is connected.');
    }

    try {
      // تشفير البيانات
      final jsonPayload = json.encode(payload);
      final encryptedPayload = AESService.encrypt(
        data: jsonPayload,
        passphrase: _encryptionPassphrase,
      );

      final headers = <String, String>{'Content-Type': 'application/json'};

      if (withAuth && _token != null) {
        headers['Authorization'] = 'Bearer $_token';
      }

      final body = json.encode({'payload': encryptedPayload});
      final response = await http.patch(
        Uri.parse('$_baseUrl$route'),
        headers: headers,
        body: body,
      );

      _debugPrint('كود الاستجابة PATCH: ${response.statusCode}');
      _debugPrint('رؤوس الاستجابة PATCH: ${json.encode(response.headers)}');

      if (response.statusCode >= 200 && response.statusCode < 400) {
        try {
          return json.decode(response.body) as Map<String, dynamic>;
        } catch (jsonError) {
          _debugPrint('خطأ في تحليل JSON في PATCH: $jsonError');
          _printDetailedError('PATCH - تحليل JSON', response, jsonError);
          return null;
        }
      } else {
        _printDetailedError('PATCH', response);
        throw Exception('فشل الطلب: ${response.statusCode} ${response.body}');
      }
    } catch (e) {
      _debugPrint('خطأ في طلب PATCH: $e');
      return null;
    }
  }

  /// إنشاء مستخدم جديد (للمدير فقط)
  Future<Map<String, dynamic>?> createUser({
    required String username,
    required String password,
    required String confirmPassword,
    required int profileId,
    required String parentId,
    String? firstname,
    String? lastname,
    String? email,
    String? phone,
  }) async {
    final payload = {
      'username': username,
      'password': password,
      'confirm_password': confirmPassword,
      'profile_id': profileId,
      'parent_id': parentId,
      if (firstname != null) 'firstname': firstname,
      if (lastname != null) 'lastname': lastname,
      if (email != null) 'email': email,
      if (phone != null) 'phone': phone,
    };
    // final res = await login();
    // print(res);
    return await postData('user', payload);
  }

  /// جلب قائمة المستخدمين (للمدير فقط)
  /// [page] رقم الصفحة المراد جلبها (افتراضي: 1)
  /// [perPage] عدد المستخدمين في كل صفحة (افتراضي: 500)
  Future<Map<String, dynamic>?> getUsers({
    int page = 1,
    int perPage = 500,
  }) async {
    final payload = {'page': page, 'per_page': perPage};
    return await postData('index/user', payload);
  }

  /// جلب بيانات المستخدم الحالي (للمستخدم)
  Future<Map<String, dynamic>?> getCurrentUser() async {
    return await getData('user');
  }

  /// تحديث بيانات المستخدم
  Future<Map<String, dynamic>?> updateUser(
    String oldName,
    String userName,
  ) async {
    final user = await getSasUserByUsername(oldName);
    return await postData('user/rename/${user!.id}', {
      "new_username": userName,
    });
  }

  /// حذف مستخدم (للمدير فقط)
  Future<Map<String, dynamic>?> deleteUser(int userId) async {
    return await postData('user/$userId/delete', {});
  }

  /// جلب إحصائيات الاستخدام
  Future<Map<String, dynamic>?> getUsageStats(int userId) async {
    return await getData('user/$userId/usage');
  }

  /// تجديد كلمة مرور المستخدم
  Future<Map<String, dynamic>?> resetPassword(
    int userId,
    String newPassword,
  ) async {
    final payload = {'password': newPassword, 'confirm_password': newPassword};

    return await postData('user/$userId/password', payload);
  }

  /// جلب بيانات الفوترة
  Future<Map<String, dynamic>?> getBilling(int userId) async {
    return await getData('user/$userId/billing');
  }

  /// إضافة دفعة جديدة
  Future<Map<String, dynamic>?> addPayment({
    required int userId,
    required double amount,
    required String paymentMethod,
    String? notes,
  }) async {
    final payload = {
      'user_id': userId,
      'amount': amount,
      'payment_method': paymentMethod,
      if (notes != null) 'notes': notes,
    };

    return await postData('payment', payload);
  }

  /// جلب مستخدم SAS Radius بواسطة اسم المستخدم
  Future<SasUser?> getSasUserByUsername(String username) async {
    try {
      print('Searching for SAS user with username: "$username"');

      // Try to fetch all users with pagination to ensure we don't miss any
      List<SasUser> allUsers = [];
      int currentPage = 1;
      int perPage = 500;
      int? totalUsers;

      do {
        print(
          'Fetching SAS users - Page $currentPage (searching for: $username)',
        );
        final response = await getUsers(page: currentPage, perPage: perPage);
        if (response != null && response.containsKey('data')) {
          final List<dynamic> usersData = response['data'];
          final List<SasUser> currentBatch = usersData
              .map((json) => SasUser.fromJson(json))
              .toList();
          allUsers.addAll(currentBatch);

          print(
            'Fetched ${currentBatch.length} users from page $currentPage (Total so far: ${allUsers.length})',
          );

          // Attempt to get total users from the response, if available
          if (response.containsKey('total') && response['total'] is int) {
            totalUsers = response['total'];
          } else if (response.containsKey('meta') &&
              response['meta']['total'] is int) {
            totalUsers = response['meta']['total'];
          }

          // If no new users are fetched in a batch, it means we've reached the end
          if (currentBatch.isEmpty && currentPage > 1) {
            break;
          }

          currentPage++;
        } else {
          break;
        }
      } while (totalUsers == null || allUsers.length < totalUsers);

      print('Total SAS users fetched for search: ${allUsers.length}');

      // Search for the user with multiple approaches
      SasUser? foundUser;

      // 1. Exact match (case-sensitive)
      foundUser = allUsers.firstWhereOrNull(
        (user) => user.username == username,
      );
      if (foundUser != null) {
        print('Found user with exact match: ${foundUser.username}');
        return foundUser;
      }

      // 2. Case-insensitive match
      foundUser = allUsers.firstWhereOrNull(
        (user) => user.username.toLowerCase() == username.toLowerCase(),
      );
      if (foundUser != null) {
        print('Found user with case-insensitive match: ${foundUser.username}');
        return foundUser;
      }

      // 3. Trimmed match (in case of extra spaces)
      foundUser = allUsers.firstWhereOrNull(
        (user) => user.username.trim() == username.trim(),
      );
      if (foundUser != null) {
        print('Found user with trimmed match: ${foundUser.username}');
        return foundUser;
      }

      // 4. Debug: Show some usernames to help identify the issue
      print('User not found. Available usernames (first 10):');
      for (int i = 0; i < (allUsers.length > 10 ? 10 : allUsers.length); i++) {
        print('  ${i + 1}. "${allUsers[i].username}" (ID: ${allUsers[i].id})');
      }

      // 5. Look for partial matches to help debug
      final partialMatches = allUsers
          .where(
            (user) =>
                user.username.contains(username) ||
                username.contains(user.username),
          )
          .toList();

      if (partialMatches.isNotEmpty) {
        print('Found ${partialMatches.length} partial matches:');
        for (var match in partialMatches) {
          print('  - "${match.username}" (ID: ${match.id})');
        }
      }

      return null;
    } catch (e) {
      print('خطأ في جلب مستخدم SAS Radius: $e');
      return null;
    }
  }

  /// جلب قائمة الباقات/البروفايلات (للمدير فقط)
  /// [page] رقم الصفحة المراد جلبها (افتراضي: 1)
  /// [perPage] عدد الباقات في كل صفحة (افتراضي: 500)
  Future<List<PackageModel>> getProfiles({
    int page = 1,
    int perPage = 500,
  }) async {
    try {
      final payload = {'page': page, 'per_page': perPage};
      final response = await postData(
        'index/profile',
        payload,
      ); // Confirmed 'index/profile' is the endpoint for packages/profiles
      if (response != null && response.containsKey('data')) {
        final List<dynamic> profilesData = response['data'];
        return profilesData.map((p) {
          return PackageModel(
            adminId: p["adminId"] ?? "",
            id:FirebaseFirestore.instance.collection('packages').doc().id,
            serverId: p['id']?.toString().trim() ?? '', // Ensure ID is string
            name: p['name']?.toString() ?? '',
            price:
                double.tryParse(p['price']?.toString() ?? '0') ??
                0, // Ensure price is double
            durationInDays:
                int.tryParse(p['duration']?.toString() ?? '30') ??
                30, // Ensure duration is int
            speed: p['speed']?.toString() ?? '',
            deviceCount:
                int.tryParse(p['device_count']?.toString() ?? '1') ??
                1, // Ensure device_count is int
            notes: p['notes']?.toString() ?? '',
            createdAt:
                DateTime.now(), // Assuming creation date is not in API, or set to now
            isActive: true, // Assuming all fetched packages are active
            sasProfileId: p['id']
                ?.toString()
                .trim(), // Set sasProfileId to the SAS profile ID
          );
        }).toList();
      }
      return [];
    } catch (e) {
      print('خطأ في جلب الباقات/البروفايلات: $e');
      return [];
    }
  }

  Future<List<UserModel>> getManagers({int page = 1, int perPage = 500}) async {
    try {
      final response = await getData(
        'index/manager',
      ); // Confirmed 'index/profile' is the endpoint for packages/profiles
      if (response != null && response.containsKey('data')) {
        final List<dynamic> profilesData = response['data'];
        return profilesData.map((p) {
          return UserModel(
            adminId: "",
            id: p['id'].toString(),
            createdAt: DateTime.now(),
            fullName: p["username"],
            password: "",
            permissions: [],
            phoneNumber: "",
            role: UserRole.admin,
            username: p['username'],
            isActive: true,
          );
        }).toList();
      }
      return [];
    } catch (e) {
      print('خطأ في جلب الباقات/البروفايلات: $e');
      return [];
    }
  }

  /// جلب رصيد SAS العام (رصيد النظام)
  Future<double?> getSasBalance() async {
    try {
      // عادة endpoint الرصيد يكون مثل: 'balance' أو 'system/balance' حسب الـ API
      final response = await getData('balance');
      if (response != null && response.containsKey('balance')) {
        return double.tryParse(response['balance'].toString());
      }
      return null;
    } catch (e) {
      print('خطأ في جلب رصيد SAS: $e');
      return null;
    }
  }

  /// تفعيل مستخدم في SAS Radius باستخدام API التفعيل الصحيح
  Future<Map<String, dynamic>?> activateUserInSas({
    required String username,
    required int newProfileId,
    String? comments,
    bool moneyCollected = true,
    bool issueInvoice = true,
  }) async {
    try {
      print(
        'Attempting to activate user "$username" with profile ID: $newProfileId',
      );

      // أولاً، جلب بيانات المستخدم الحالي في SAS
      final sasUser = await getSasUserByUsername(username);
      if (sasUser == null) {
        throw Exception('المستخدم $username غير موجود في SAS Radius.');
      }

      print(
        'Found SAS user: ${sasUser.username} (ID: ${sasUser.id}, Current Profile: ${sasUser.profileId})',
      );

      // إنشاء UUID فريد للمعاملة
      final transactionId = const Uuid().v4();

      // إعداد البيانات للتفعيل باستخدام API SAS4 الصحيح
      final activationData = {
        'method': 'credit',
        'pin': '',
        'user_id': sasUser.id.toString(),
        'money_collected': moneyCollected,
        'comments': comments ?? 'Activation from ISP Manager',
        'issue_invoice': issueInvoice,
        'transaction_id': transactionId,
        'profile_id': newProfileId, // إضافة معرف الباقة الجديدة
      };

      print('Activation data: $activationData');
      // تشفير البيانات باستخدام AES
      final encryptedPayload = AESService.encrypt(
        data: jsonEncode(activationData),
        passphrase: _encryptionPassphrase,
      );
      print('Encrypted payload generated successfully');

      // إعداد HTTP client يدعم SSL غير الموثق
      final httpClient = HttpClient()
        ..badCertificateCallback = (cert, host, port) => true;
      final ioClient = io_client.IOClient(
        httpClient,
      ); // إعداد URL الكامل لـ API التفعيل - استخدام المسار المباشر
      String p = _portal == 'ucp' ? 'user' : 'admin';
      final activationUrl = Uri.parse(
        'https://$_host/$p/api/index.php/api/user/activate',
      );
      print('Making request to: $activationUrl');

      // إعداد الطلب
      final request = http.Request('POST', activationUrl);

      // إضافة Headers
      request.headers.addAll({
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': 'Bearer $_token',
        'Accept': 'application/json',
      });

      // إضافة البيانات المشفرة كـ payload
      request.bodyFields = {'payload': encryptedPayload};

      print('Sending activation request...');

      // إرسال الطلب
      final streamedResponse = await ioClient.send(request);
      final response = await http.Response.fromStream(streamedResponse);

      ioClient.close();

      print('Response status: ${response.statusCode}');
      print('Response body: ${response.body}');

      if (response.statusCode == 200) {
        // محاولة فك تشفير الاستجابة إذا كانت مشفرة
        String responseBody = response.body;

        try {
          // إذا كانت الاستجابة مشفرة، فك تشفيرها
          if (responseBody.isNotEmpty &&
              !responseBody.startsWith('{') &&
              !responseBody.startsWith('[')) {
            responseBody = AESService.decrypt(
              base64Data: responseBody,
              passphrase: _encryptionPassphrase,
            );
            print('Decrypted response: $responseBody');
          }

          final responseData = jsonDecode(responseBody);
          print('Activation successful: $responseData');

          return {
            'status': 'success',
            'message': 'تم تفعيل المستخدم بنجاح',
            'data': responseData,
            'transaction_id': transactionId,
          };
        } catch (decodeError) {
          print(
            'Could not decode response as JSON, treating as success: $decodeError',
          );
          return {
            'status': 'success',
            'message': 'تم تفعيل المستخدم بنجاح',
            'raw_response': responseBody,
            'transaction_id': transactionId,
          };
        }
      } else {
        print('Activation failed with status: ${response.statusCode}');
        return {
          'status': 'error',
          'message': 'فشل في تفعيل المستخدم: ${response.reasonPhrase}',
          'status_code': response.statusCode,
          'response_body': response.body,
        };
      }
    } catch (e) {
      print('خطأ في تفعيل مستخدم SAS Radius: $e');
      return {
        'status': 'error',
        'message': 'خطأ في تفعيل المستخدم: ${e.toString()}',
      };
    }
  }

  /// تسجيل الخروج
  void logout() {
    _token = null;
  }

  /// التحقق من حالة الاتصال
  bool get isLoggedIn => _token != null;

  /// اختبار اتصال الخادم وتشخيص المشاكل
  Future<Map<String, dynamic>> testServerConnection({
    required String host,
    String portal = 'acp',
  }) async {
    final testResult = <String, dynamic>{
      'host': host,
      'portal': portal,
      'timestamp': DateTime.now().toIso8601String(),
      'tests': <String, dynamic>{},
    };

    try {
      _debugPrint('=== بدء اختبار الخادم ===');
      _debugPrint('المضيف: $host');
      _debugPrint('البوابة: $portal');

      // 1. اختبار الوصول الأساسي للخادم
      String p = portal == 'ucp' ? 'user' : 'admin';
      String baseUrl = 'http://$host/$p/api/index.php/api/';

      _debugPrint('اختبار الرابط الأساسي: $baseUrl');

      // اختبار بسيط لـ ping الخادم
      testResult['tests']['basic_connectivity'] = await _testBasicConnectivity(
        host,
      );

      // اختبار الوصول لصفحة API الأساسية
      testResult['tests']['api_endpoint'] = await _testApiEndpoint(baseUrl);

      // اختبار endpoint تسجيل الدخول
      String loginRoute = portal == 'ucp' ? 'auth/login' : 'login';
      testResult['tests']['login_endpoint'] = await _testLoginEndpoint(
        baseUrl + loginRoute,
      );

      // تقييم النتائج الإجمالية
      testResult['overall_status'] = _evaluateTestResults(testResult['tests']);
    } catch (e) {
      testResult['error'] = e.toString();
      testResult['overall_status'] = 'failed';
      _debugPrint('خطأ في اختبار الخادم: $e');
    }

    _debugPrint('=== نتائج اختبار الخادم ===');
    _debugPrint(json.encode(testResult));
    _debugPrint('===========================');

    return testResult;
  }

  /// اختبار الاتصال الأساسي بالخادم
  Future<Map<String, dynamic>> _testBasicConnectivity(String host) async {
    try {
      final response = await http
          .get(
            Uri.parse('http://$host'),
            headers: {'User-Agent': 'ISP-Manager-App/1.0'},
          )
          .timeout(Duration(seconds: 10));

      return {
        'status': 'success',
        'status_code': response.statusCode,
        'reachable': true,
        'response_time': 'fast', // يمكن تحسين هذا لقياس الوقت الفعلي
      };
    } catch (e) {
      return {'status': 'failed', 'reachable': false, 'error': e.toString()};
    }
  }

  /// اختبار الوصول لـ API endpoint
  Future<Map<String, dynamic>> _testApiEndpoint(String apiUrl) async {
    try {
      final response = await http
          .get(
            Uri.parse(apiUrl),
            headers: {
              'Content-Type': 'application/json',
              'User-Agent': 'ISP-Manager-App/1.0',
            },
          )
          .timeout(Duration(seconds: 15));

      final analysis = _analyzeServerResponse(response);

      return {
        'status': response.statusCode < 500 ? 'accessible' : 'server_error',
        'status_code': response.statusCode,
        'content_analysis': analysis,
        'api_responsive': response.statusCode != 404,
      };
    } catch (e) {
      return {
        'status': 'failed',
        'error': e.toString(),
        'api_responsive': false,
      };
    }
  }

  /// اختبار endpoint تسجيل الدخول
  Future<Map<String, dynamic>> _testLoginEndpoint(String loginUrl) async {
    try {
      // إرسال طلب فارغ لاختبار استجابة endpoint
      final response = await http
          .post(
            Uri.parse(loginUrl),
            headers: {
              'Content-Type': 'application/json',
              'User-Agent': 'ISP-Manager-App/1.0',
            },
            body: json.encode({}),
          )
          .timeout(Duration(seconds: 15));

      final analysis = _analyzeServerResponse(response);

      return {
        'status': _evaluateLoginEndpointResponse(response.statusCode, analysis),
        'status_code': response.statusCode,
        'content_analysis': analysis,
        'login_endpoint_available': response.statusCode != 404,
      };
    } catch (e) {
      return {
        'status': 'failed',
        'error': e.toString(),
        'login_endpoint_available': false,
      };
    }
  }

  /// تقييم استجابة endpoint تسجيل الدخول
  String _evaluateLoginEndpointResponse(
    int statusCode,
    Map<String, dynamic> analysis,
  ) {
    if (statusCode == 404) return 'not_found';
    if (statusCode >= 500) return 'server_error';
    if (statusCode == 400 || statusCode == 401)
      return 'endpoint_working'; // يتوقع بيانات صحيحة
    if (analysis['is_html'] == true)
      return 'html_response'; // يعيد HTML بدلاً من JSON
    return 'accessible';
  }

  /// تقييم النتائج الإجمالية للاختبار
  String _evaluateTestResults(Map<String, dynamic> tests) {
    if (tests['basic_connectivity']?['reachable'] != true) {
      return 'host_unreachable';
    }

    if (tests['api_endpoint']?['api_responsive'] != true) {
      return 'api_not_available';
    }

    if (tests['login_endpoint']?['login_endpoint_available'] != true) {
      return 'login_endpoint_missing';
    }

    if (tests['api_endpoint']?['content_analysis']?['is_html'] == true) {
      return 'server_misconfigured'; // يعيد HTML بدلاً من API
    }

    return 'server_responsive';
  }

  /// مقارنة خادمين وإظهار الاختلافات
  Future<Map<String, dynamic>> compareServers({
    required String workingHost,
    required String problematicHost,
    String portal = 'acp',
  }) async {
    _debugPrint('=== مقارنة الخوادم ===');
    _debugPrint('الخادم العامل: $workingHost');
    _debugPrint('الخادم المشكوك فيه: $problematicHost');

    final comparison = <String, dynamic>{
      'working_server': workingHost,
      'problematic_server': problematicHost,
      'portal': portal,
      'timestamp': DateTime.now().toIso8601String(),
      'comparison_results': <String, dynamic>{},
    };

    try {
      // اختبار كلا الخادمين
      final workingResults = await testServerConnection(
        host: workingHost,
        portal: portal,
      );
      final problematicResults = await testServerConnection(
        host: problematicHost,
        portal: portal,
      );

      comparison['working_server_results'] = workingResults;
      comparison['problematic_server_results'] = problematicResults;

      // تحليل الاختلافات
      comparison['comparison_results'] = _analyzeServerDifferences(
        workingResults,
        problematicResults,
      );

      // اقتراحات الحل
      comparison['recommendations'] = _generateRecommendations(
        comparison['comparison_results'],
      );
    } catch (e) {
      comparison['error'] = e.toString();
    }

    _debugPrint('=== نتائج المقارنة ===');
    _debugPrint(json.encode(comparison));
    _debugPrint('======================');

    return comparison;
  }

  /// تحليل الاختلافات بين خادمين
  Map<String, dynamic> _analyzeServerDifferences(
    Map<String, dynamic> workingResults,
    Map<String, dynamic> problematicResults,
  ) {
    final differences = <String, dynamic>{};

    // مقارنة الحالة الإجمالية
    differences['overall_status'] = {
      'working': workingResults['overall_status'],
      'problematic': problematicResults['overall_status'],
      'same':
          workingResults['overall_status'] ==
          problematicResults['overall_status'],
    };

    // مقارنة الاتصال الأساسي
    final workingConn = workingResults['tests']?['basic_connectivity'];
    final problematicConn = problematicResults['tests']?['basic_connectivity'];

    differences['connectivity'] = {
      'working_reachable': workingConn?['reachable'] ?? false,
      'problematic_reachable': problematicConn?['reachable'] ?? false,
      'both_reachable':
          (workingConn?['reachable'] ?? false) &&
          (problematicConn?['reachable'] ?? false),
    };

    // مقارنة API endpoint
    final workingApi = workingResults['tests']?['api_endpoint'];
    final problematicApi = problematicResults['tests']?['api_endpoint'];

    differences['api_endpoint'] = {
      'working_responsive': workingApi?['api_responsive'] ?? false,
      'problematic_responsive': problematicApi?['api_responsive'] ?? false,
      'working_status_code': workingApi?['status_code'],
      'problematic_status_code': problematicApi?['status_code'],
      'status_codes_match':
          workingApi?['status_code'] == problematicApi?['status_code'],
    };

    // مقارنة محتوى الاستجابة
    final workingContent = workingApi?['content_analysis'];
    final problematicContent = problematicApi?['content_analysis'];

    differences['content_type'] = {
      'working_is_html': workingContent?['is_html'] ?? false,
      'problematic_is_html': problematicContent?['is_html'] ?? false,
      'working_is_json': workingContent?['is_json'] ?? false,
      'problematic_is_json': problematicContent?['is_json'] ?? false,
      'content_types_match':
          (workingContent?['is_html'] == problematicContent?['is_html']) &&
          (workingContent?['is_json'] == problematicContent?['is_json']),
    };

    return differences;
  }

  /// إنشاء اقتراحات الحل بناءً على تحليل الاختلافات
  List<String> _generateRecommendations(Map<String, dynamic> differences) {
    final recommendations = <String>[];

    // فحص الاتصال الأساسي
    if (differences['connectivity']?['problematic_reachable'] == false) {
      recommendations.add('الخادم المشكوك فيه غير قابل للوصول. تحقق من:');
      recommendations.add('- اتصال الإنترنت');
      recommendations.add('- عنوان الخادم الصحيح');
      recommendations.add('- إعدادات الجدار الناري (Firewall)');
      recommendations.add('- إعدادات DNS');
    }

    // فحص API endpoint
    if (differences['api_endpoint']?['problematic_responsive'] == false) {
      recommendations.add('API غير متاح على الخادم المشكوك فيه. تحقق من:');
      recommendations.add('- تثبيت SAS Radius بشكل صحيح');
      recommendations.add('- تفعيل API في إعدادات SAS');
      recommendations.add('- تكوين خادم الويب (Apache/Nginx)');
    }

    // فحص نوع المحتوى
    if (differences['content_type']?['problematic_is_html'] == true &&
        differences['content_type']?['working_is_html'] == false) {
      recommendations.add(
        'الخادم المشكوك فيه يعيد HTML بدلاً من JSON. هذا يشير إلى:',
      );
      recommendations.add('- خطأ في تكوين خادم الويب');
      recommendations.add('- مشكلة في routing للطلبات');
      recommendations.add('- SAS Radius غير مفعل أو معطل');
      recommendations.add('- صفحة خطأ يتم عرضها بدلاً من API');
    }

    // مقارنة status codes
    if (!differences['api_endpoint']?['status_codes_match']) {
      final workingCode = differences['api_endpoint']?['working_status_code'];
      final problematicCode =
          differences['api_endpoint']?['problematic_status_code'];
      recommendations.add(
        'رموز الاستجابة مختلفة: العامل ($workingCode) vs المشكوك ($problematicCode)',
      );

      if (problematicCode == 404) {
        recommendations.add('- خطأ 404: تأكد من مسار API الصحيح');
        recommendations.add('- تحقق من تثبيت SAS Radius');
      } else if (problematicCode >= 500) {
        recommendations.add('- خطأ خادم داخلي: فحص سجلات الخادم مطلوب');
        recommendations.add(
          '- قد تكون هناك مشكلة في قاعدة البيانات أو التكوين',
        );
      }
    }

    if (recommendations.isEmpty) {
      recommendations.add('لا توجد اختلافات واضحة. قد تكون المشكلة في:');
      recommendations.add('- بيانات الاعتماد (اسم المستخدم/كلمة المرور)');
      recommendations.add('- إعدادات الصلاحيات في SAS');
      recommendations.add('- تكوين API خاص بهذا الخادم');
    }

    return recommendations;
  }

  /// دالة لاختبار اتصال خادم مع واجهة بسيطة للمستخدم
  Future<String> testServerConnectionSimple(String host) async {
    try {
      _debugPrint('=== اختبار اتصال الخادم: $host ===');

      final result = await testServerConnection(host: host);
      final status = result['overall_status'] as String;

      switch (status) {
        case 'host_unreachable':
          return 'الخادم غير قابل للوصول. تحقق من اتصال الإنترنت وعنوان الخادم.';
        case 'api_not_available':
          return 'API غير متاح. تحقق من تثبيت وتفعيل SAS Radius على الخادم.';
        case 'login_endpoint_missing':
          return 'endpoint تسجيل الدخول مفقود. تحقق من تكوين SAS Radius.';
        case 'server_misconfigured':
          return 'الخادم يعيد HTML بدلاً من JSON. مشكلة في تكوين خادم الويب.';
        case 'server_responsive':
          return 'الخادم يستجيب بشكل صحيح. يمكنك المحاولة مرة أخرى.';
        default:
          return 'حالة غير معروفة: $status';
      }
    } catch (e) {
      return 'خطأ في اختبار الخادم: $e';
    }
  }

  /// إنشاء HTTP client يتعامل مع شهادات SSL الموقعة ذاتياً
  http.Client _createHttpClient() {
    final httpClient = HttpClient();

    // تجاهل أخطاء شهادات SSL للخوادم التي تستخدم شهادات موقعة ذاتياً
    httpClient.badCertificateCallback =
        (X509Certificate cert, String host, int port) {
          _debugPrint('تجاهل خطأ شهادة SSL للخادم: $host:$port');
          return true; // السماح بجميع الشهادات
        };

    return io_client.IOClient(httpClient);
  }

  /// تغيير باقة مستخدم في SAS Radius
  Future<Map<String, dynamic>?> changeUserProfile({
    required String username,
    required int newProfileId,
    String method = 'credit', // credit أو reward_points
    String? comments,
  }) async {
    try {
      print(
        'Attempting to change profile for user "$username" to profile ID: $newProfileId',
      );

      // أولاً، جلب بيانات المستخدم الحالي في SAS
      final sasUser = await getSasUserByUsername(username);
      if (sasUser == null) {
        throw Exception('المستخدم $username غير موجود في SAS Radius.');
      }

      print(
        'Found SAS user: ${sasUser.username} (ID: ${sasUser.id}, Current Profile: ${sasUser.profileId})',
      ); // التحقق من أن الباقة مختلفة عن الحالية
      if (sasUser.profileId == newProfileId) {
        return {
          'status': 'info',
          'message': 'المستخدم يستخدم نفس الباقة بالفعل (${sasUser.profileId})',
          'current_profile_id': sasUser.profileId,
          'requested_profile_id': newProfileId,
        };
      }

      // إنشاء UUID فريد للمعاملة
      final transactionId = const Uuid().v4();

      // إعداد البيانات لتغيير الباقة باستخدام API SAS4 الصحيح
      final changeProfileData = {
        'user_id': sasUser.id.toString(),
        'profile_id': newProfileId.toString(),
        'method': method,
        'transaction_id': transactionId,
      };

      print('Change profile data: $changeProfileData');

      // تشفير البيانات باستخدام AES
      final encryptedPayload = AESService.encrypt(
        data: jsonEncode(changeProfileData),
        passphrase: _encryptionPassphrase,
      );
      print('Encrypted payload generated successfully');

      // إعداد HTTP client يدعم SSL غير الموثق
      final httpClient = HttpClient()
        ..badCertificateCallback = (cert, host, port) => true;
      final ioClient = io_client.IOClient(httpClient);

      // إعداد URL الكامل لـ API تغيير الباقة
      String p = _portal == 'ucp' ? 'user' : 'admin';
      final changeProfileUrl = Uri.parse(
        'https://$_host/$p/api/index.php/api/user/changeProfile',
      );
      print('Making request to: $changeProfileUrl');

      // إعداد الطلب
      final request = http.Request('POST', changeProfileUrl);

      // إضافة Headers
      request.headers.addAll({
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': 'Bearer $_token',
        'Accept': 'application/json',
      });

      // إضافة البيانات المشفرة كـ payload
      request.bodyFields = {'payload': encryptedPayload};

      print('Sending change profile request...');

      // إرسال الطلب
      final streamedResponse = await ioClient.send(request);
      final response = await http.Response.fromStream(streamedResponse);

      ioClient.close();

      print('Response status: ${response.statusCode}');
      print('Response body: ${response.body}');

      if (response.statusCode == 200) {
        // محاولة فك تشفير الاستجابة إذا كانت مشفرة
        String responseBody = response.body;

        try {
          // إذا كانت الاستجابة مشفرة، فك تشفيرها
          if (responseBody.isNotEmpty &&
              !responseBody.startsWith('{') &&
              !responseBody.startsWith('[')) {
            responseBody = AESService.decrypt(
              base64Data: responseBody,
              passphrase: _encryptionPassphrase,
            );
            print('Decrypted response: $responseBody');
          }

          final responseData = jsonDecode(responseBody);
          print('Profile change successful: $responseData');

          return {
            'status': 'success',
            'message': 'تم تغيير الباقة بنجاح',
            'data': responseData,
            'transaction_id': transactionId,
            'old_profile_id': sasUser.profileId,
            'new_profile_id': newProfileId,
          };
        } catch (decodeError) {
          print(
            'Could not decode response as JSON, treating as success: $decodeError',
          );
          return {
            'status': 'success',
            'message': 'تم تغيير الباقة بنجاح',
            'raw_response': responseBody,
            'transaction_id': transactionId,
            'old_profile_id': sasUser.profileId,
            'new_profile_id': newProfileId,
          };
        }
      } else {
        print('Profile change failed with status: ${response.statusCode}');
        return {
          'status': 'error',
          'message': 'فشل في تغيير الباقة: ${response.reasonPhrase}',
          'status_code': response.statusCode,
          'response_body': response.body,
        };
      }
    } catch (e) {
      print('خطأ في تغيير باقة مستخدم SAS Radius: $e');
      return {
        'status': 'error',
        'message': 'خطأ في تغيير الباقة: ${e.toString()}',
      };
    }
  }

  Future<SasUser?> getSasUSerDetails(String username) async {
    try {
      

      // Try to fetch all users with pagination to ensure we don't miss any
  final   sasUser =    await getSasUserByUsername(username);
      final response = await getData("user/overview/${sasUser!.id}");
      print(response);
      if (response != null && response.containsKey('data')) {
        final Map<String, dynamic> usersData = response['data'];
        final user = SasUser.fromJson(usersData);
        return user;
      }
      return null;
    } catch (e) {
      print('خطأ في جلب مستخدم SAS Radius: $e');
      return null;
    }
  }
}
